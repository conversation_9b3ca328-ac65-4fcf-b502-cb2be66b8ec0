import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:dropx/src/core/shared/extensions/context_extensions.dart';
import 'package:dropx/src/core/shared/extensions/riverpod_extensions.dart';
import 'package:dropx/src/core/theme/color_manager.dart';
import 'package:dropx/src/screens/orders/models/station.model.dart';
import 'package:dropx/src/screens/shipment_search/models/search_request.model.dart';
import 'package:dropx/src/screens/shipment_search/models/delivery_search_response.model.dart';
import 'package:dropx/src/screens/shipment_search/providers/shipment_search_providers.dart';
import 'package:dropx/src/screens/orders/providers/orders_providers.dart';
import 'package:dropx/src/screens/orders/models/type.model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../generated/assets.gen.dart';

class SearchResultsScreen extends HookConsumerWidget {
  final SearchRequest searchRequest;
  final Station fromStation;
  final Station toStation;

  const SearchResultsScreen({
    super.key,
    required this.searchRequest,
    required this.fromStation,
    required this.toStation,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expandedIndex = useState<int?>(-1);
    final currentAttendanceRequest = useState<AttendanceRequest?>(null);
    final appLifecycleState = useAppLifecycleState();

    // Get types for mapping type names to IDs
    final typesAsync = ref.watch(getTypesFutureProvider);

    // Handle app lifecycle changes
    useEffect(() {
      if (appLifecycleState == AppLifecycleState.paused ||
          appLifecycleState == AppLifecycleState.detached) {
        // App is being closed or paused, delete attendance if any
        if (currentAttendanceRequest.value != null) {
          ref.read(shipmentSearchControllerProvider).deleteAttendance();
          currentAttendanceRequest.value = null;
        }
      }
      return null;
    }, [appLifecycleState]);

    final searchResultsAsync =
        ref.watch(searchDeliveryFutureProvider(searchRequest));

    void onExpansionChanged(
        int index, bool isExpanded, DeliverySearchItem item) async {
      if (isExpanded) {
        // Close other tiles first
        if (expandedIndex.value != -1 && expandedIndex.value != index) {
          // Delete previous attendance
          if (currentAttendanceRequest.value != null) {
            await ref.read(shipmentSearchControllerProvider).deleteAttendance();
          }
        }

        expandedIndex.value = index;

        // Add attendance for this item
        final types = typesAsync.value;
        final attendanceRequest = AttendanceRequest(
          fromStationId: searchRequest.fromStationId,
          toStationId: searchRequest.toStationId,
          typeId: _getTypeIdFromTypeName(item.typeName, types),
        );

        try {
          await ref.read(shipmentSearchControllerProvider).addAttendance(
                attendanceRequest: attendanceRequest,
              );
          currentAttendanceRequest.value = attendanceRequest;
        } catch (e) {
          if (context.mounted) {
            showToast(context.tr.error, isError: true);
          }
        }
      } else {
        expandedIndex.value = -1;

        // Delete attendance when closing
        if (currentAttendanceRequest.value != null) {
          try {
            await ref.read(shipmentSearchControllerProvider).deleteAttendance();
            currentAttendanceRequest.value = null;
          } catch (e) {
            if (context.mounted) {
              showToast(context.tr.error, isError: true);
            }
          }
        }
      }
    }

    void onIAmHerePressed() async {
      if (currentAttendanceRequest.value != null) {
        try {
          // Delete attendance first
          await ref.read(shipmentSearchControllerProvider).deleteAttendance();

          // Add interest
          final interestRequest = InterestRequest(
            fromStationId: currentAttendanceRequest.value!.fromStationId,
            toStationId: currentAttendanceRequest.value!.toStationId,
            typeId: currentAttendanceRequest.value!.typeId,
          );

          await ref.read(shipmentSearchControllerProvider).addInterest(
                interestRequest: interestRequest,
              );

          currentAttendanceRequest.value = null;
          expandedIndex.value = -1;

          if (context.mounted) {
            showToast(context.tr.interestAdded);
          }
        } catch (e) {
          if (context.mounted) {
            showToast(context.tr.error, isError: true);
          }
        }
      }
    }

    return PopScope(
      onPopInvokedWithResult: (didPop, result) {
        ref.read(shipmentSearchControllerProvider).deleteAttendance();
      },
      child: Scaffold(
        backgroundColor: ColorManager.white,
        appBar: AppBar(
          title: Text(
            context.tr.searchShipmentDetails,
            style: AppTextStyles.title,
          ),
          backgroundColor: ColorManager.white,
          surfaceTintColor: ColorManager.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ),
        body: searchResultsAsync.get<Widget>(
          data: (searchResults) {
            if (searchResults.data.isEmpty) {
              return Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 80,
                      color: ColorManager.greyIcon,
                    ),
                    AppGaps.gap16,
                    Text(
                      context.tr.noShipmentsFound,
                      style: AppTextStyles.headlineSmall.copyWith(
                        color: ColorManager.greyText,
                      ),
                    ),
                  ],
                ),
              );
            }

            return Column(
              children: [
                AppGaps.gap8,

                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: ColorManager.primaryColor,
                        size: 20,
                      ),
                      AppGaps.gap8,
                      Text(
                        context.tr.selectedRoute,
                        style: AppTextStyles.labelLarge.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),

                // Selected Route Display
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: Border.all(color: ColorManager.greyBorder),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              fromStation.name,
                              style: AppTextStyles.subTitle.copyWith(
                                color: ColorManager.darkGrey,
                              ),
                            ),
                          ),
                          const Icon(
                            Icons.arrow_forward,
                            color: ColorManager.primaryColor,
                          ),
                          Expanded(
                            child: Text(
                              toStation.name,
                              style: AppTextStyles.subTitle.copyWith(
                                color: ColorManager.darkGrey,
                              ),
                              textAlign: TextAlign.end,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Shipment Types List
                Expanded(
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    itemCount: searchResults.data.length,
                    itemBuilder: (context, index) {
                      final item = searchResults.data[index];

                      return Container(
                        margin: const EdgeInsets.only(bottom: 12),
                        decoration: BoxDecoration(
                          border: Border.all(color: ColorManager.greyBorder),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: ExpansionTile(
                          title: Row(
                            children: [
                              Assets.icons.packageShipment.image(
                                width: 25,
                              ),
                              AppGaps.gap8,
                              Text(
                                '${context.tr.shipmentsUpTo} ${item.typeName}',
                                style: AppTextStyles.labelLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          initiallyExpanded: false,
                          onExpansionChanged: (expanded) =>
                              onExpansionChanged(index, expanded, item),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _buildInfoRow(
                                    context,
                                    Icons.local_shipping,
                                    context.tr.shipmentsCount,
                                    item.shipmentsCount.toString(),
                                  ),
                                  AppGaps.gap8,
                                  if (item.interestedPeople == 0)
                                    Text(
                                      context.tr.noPeopleInterestOnThis,
                                      style: AppTextStyles.bodyMedium.copyWith(
                                        color: ColorManager.greyText,
                                      ),
                                    )
                                  else
                                    _buildInfoRow(
                                      context,
                                      Icons.people,
                                      context.tr.interestedPeople,
                                      item.interestedPeople.toString(),
                                    ),
                                  AppGaps.gap8,
                                  _buildInfoRow(
                                    context,
                                    Icons.percent,
                                    context.tr.deliveryChance,
                                    item.deliveryChance,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

                // I Am Here Button
                if (currentAttendanceRequest.value != null)
                  SafeArea(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Button(
                        label: context.tr.iAmHere,
                        onPressed: onIAmHerePressed,
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  ),
              ],
            );
          },
          error: (error, stackTrace) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error,
                    size: 80,
                    color: ColorManager.greyIcon,
                  ),
                  AppGaps.gap16,
                  Text(
                    context.tr.error,
                    style: AppTextStyles.headlineSmall.copyWith(
                      color: ColorManager.greyText,
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildInfoRow(
      BuildContext context, IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: ColorManager.greyIcon,
        ),
        AppGaps.gap8,
        Text(
          '$label: ',
          style: AppTextStyles.bodyMedium.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        Text(
          value,
          style: AppTextStyles.bodyMedium.copyWith(
            color: ColorManager.primaryColor,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  int _getTypeIdFromTypeName(String typeName, List<OrderType>? types) {
    if (types == null) return 1; // Default fallback

    // Try to find the type by name
    final matchingType =
        types.where((type) => type.name == typeName).firstOrNull;
    return matchingType?.id ?? 1; // Return ID if found, otherwise default to 1
  }
}
